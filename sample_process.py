"""This is an additional example to my medium article about combining python multithreading- and processing:

https://medium.com/@sampsa.riikonen/doing-python-multiprocessing-the-right-way-a54c1880e300

That article is a pre-requisite for you to understand this code, so you *must* read it first.

You might also want to read:

- Python's select module tutorial
- Linux shmem documentation

Two examples are included here:

Example 1:

    - Handling interprocess communications properly from the main process with the subprocesses
    - Posix shared memory is used to pass a numpy array to the multiprocess and getting another one in return
    - With shmem you might need to clean the shmem every now and then, especially if you program crashes, with: "rm -f /dev/shm/*"
"""

import select
import signal
import time
from contextlib import contextmanager
from multiprocessing import Process, Pipe
from multiprocessing.managers import SharedMemoryManager

import numpy as np


# *** EXAMPLE 1 ***

class MyProcess(Process):

    def __init__(self, name="nada", smm=None):
        super().__init__()
        self.active = False
        self.name = name
        self.front_pipe, self.back_pipe = Pipe()

        # Create a model numpy array
        self.array_shape = (6,)
        self.array_dtype = np.float64
        a = np.array([1, 2, 3, 4, 5, 6], dtype=self.array_dtype)

        # Use SharedMemoryManager's create_shm method instead
        self.shm_in = smm.SharedMemory(size=a.nbytes)
        self.shm_out = smm.SharedMemory(size=a.nbytes)

        # Create NumPy arrays from shared memory
        self.arr_in = np.ndarray(a.shape, dtype=a.dtype, buffer=self.shm_in.buf)
        self.arr_out = np.ndarray(a.shape, dtype=a.dtype, buffer=self.shm_out.buf)

    # BACKEND

    def run(self):
        # ignore CTRL-C in this subprocess
        signal.signal(signal.SIGINT, signal.SIG_IGN)

        # Create NumPy arrays from shared memory
        a = np.array([1, 2, 3, 4, 5, 6], dtype=self.array_dtype)
        self.arr_in = np.ndarray(a.shape, dtype=a.dtype, buffer=self.shm_in.buf)
        self.arr_out = np.ndarray(a.shape, dtype=a.dtype, buffer=self.shm_out.buf)

        try:
            self.active = True
            while self.active:
                self.active = self.listen_front__()
        except Exception as e:
            print(f"Error in process {self.name}: {e}")
        finally:
            print(f"Process {self.name} shutting down")

    def listen_front__(self):
        try:
            if self.back_pipe.poll(timeout=1.0):  # Add timeout for more responsive shutdown
                message = self.back_pipe.recv()
                if message == "stop":
                    return False
                elif message == "doCalculation":
                    self.do_calculation__()
                    return True
                else:
                    print(f"listenFront__ : unknown message: {message}")
                    return True
            return True
        except EOFError:  # Handle pipe closure
            return False

    def do_calculation__(self):
        print(f"backend: {self.name} doing calculation with {self.arr_in}")
        time.sleep(2)  # simulate a heavy calculation
        self.arr_out[:] = np.random.randint(1, 10, size=self.array_shape)
        print(f"backend: {self.name} calculations results: {self.arr_out}")
        self.back_pipe.send("ready")

    # FRONTEND

    def get_pipe(self):
        return self.front_pipe

    def do_calculation(self):
        print(f"frontend: {self.name} will do calculation with {self.arr_in}")
        self.front_pipe.send("doCalculation")

    def stop(self):
        self.front_pipe.send("stop")
        self.join(timeout=5)  # Add timeout for safety
        if self.is_alive():
            self.terminate()
            self.join()


@contextmanager
def create_processes(num_processes=2):
    """Context manager to handle process creation and cleanup"""
    with SharedMemoryManager() as smm:
        processes = []
        try:
            for i in range(num_processes):
                p = MyProcess(name=f"test_process_{i + 1}", smm=smm)
                p.start()
                processes.append(p)
            yield processes
        finally:
            for p in processes:
                try:
                    p.stop()
                except:
                    pass


def main1():
    """Example 1 with improved error handling and cleanup"""
    with create_processes(2) as processes:
        p1, p2 = processes
        p1_pipe = p1.get_pipe()
        p2_pipe = p2.get_pipe()

        # start calculating something
        p1.arr_in[:] = 1.0
        p2.arr_in[:] = 2.0
        p1.do_calculation()
        p2.do_calculation()

        try:
            while True:
                rlis = [p1_pipe, p2_pipe]
                r, w, e = select.select(rlis, [], [], 1)  # one sec timeout

                for pipe in r:
                    process = p1 if pipe == p1_pipe else p2
                    msg = pipe.recv()
                    print(f"main process: message from {process.name}: {msg}")

                    if msg == "ready":
                        print(f"main process: got from {process.name}: {process.arr_out}")
                        process.arr_in[:] = np.random.randint(1, 5)
                        process.do_calculation()

                if not r:
                    print("main process: no messages from subprocesses but I'm still alive")

        except KeyboardInterrupt:
            print("you pressed CTRL-C: I will exit")


if __name__ == '__main__':
    main1()
