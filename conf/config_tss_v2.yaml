username: fu.sunfu

network:
  n_label: 2
  wide_field_dims: 0
  wide_hidden_units: [ 512, 256 ]
  wide_seq_hidden_units: [ 512, 256, 64 ]
  user_entity_attr_size: 0
  user_entity_event_cnt: 0
  embedding_size: 64 # input feature embedding dimension & refer api bert emebed dimension
  dropout: 0.1 # dropout
  use_gpu: 0
  network_form: 'transformer_wide'

training:
  debug: 0 # training data from csv
  base_epoch: 1 # model load last time saved weight,and continue training
  save_best_epoch: 1
  gpu_parallel: 1
  device_id: 0
  epoch: 2
  validation_epoch: 1
  batch_size: 8
  val_batch_size: 32
  pred_batch_size: 1024
  learning_rate: 0.0000001
  if_get_attention: False
  save_freq: 100
  print_freq: 100
  tb_folder: '/home/<USER>/ds-personal-sunfu/model_log/anti-crawler-user-seq-training/log'
  save_folder: '/home/<USER>/ds-personal-sunfu/model_log/anti-crawler-user-seq-training/model_weight_v2/'
  fraud_ratio: 10
  training_start_date: '2024-06-03'
  training_end_date: '2024-06-03'
  validate_start_date: '2024-05-20'
  validate_end_date: '2024-05-27'
  test_start_date: '2024-06-07'
  test_end_date: '2024-06-07'

predict:
  model_file_path: 'hdfs://R2/projects/regds_antifraud/hdfs/prod/generic_user/cbk/model_weights_v1_0/transformer_wide_best_epoch_25_best_acc_0.9593_2022_12_28_1621.pth'
  eval_image_path: './performance_validation/curve_image'

crawler_standard_params:
  feats_ids_dict_file: './crawler_standard_params_v2/prod_feats_ids_dict.json'
  lib_refer_path_keywords: './crawler_standard_params_v2/lib_refer_path_keywords.pkl'
  lib_api_path_keywords: './crawler_standard_params_v2/lib_api_path_keywords.pkl'
  wide_stddev: './crawler_standard_params_v2/wide_stddev.json'
  wide_mean: './crawler_standard_params_v2/wide_mean.json'

path:
  predict_path: 'hdfs://R2/projects/regds_antifraud/hive/regds_antifraud/dwd_anti_crawler_seq_model_predict_di'
  predict_us_path: 'hdfs://D2/projects/regds_antifraud/hive/regds_antifraud/dwd_anti_crawler_seq_model_predict_di'
  predict_file_system_path: '/home/<USER>/ds-personal-sunfu-us/model_log/anti-crawler-user-seq-training/predict'
  dwd_anti_crawler_tss_refer_api_df_path: 'hdfs://R2/projects/regds_antifraud/hive/regds_antifraud/dwd_anti_crawler_tss_refer_api_df'
  refer_api_bert_embed_path: 'hdfs://R2/projects/regds_antifraud/hdfs/prod/anti_crawler/refer_api_bert_embed_df/'
  user_id_ids_val_seq_path: 'hdfs://R2/projects/regds_antifraud/hdfs/prod/anti_crawler/user_ids_val_seq'
  user_id_redundancy_event_seq_path: 'hdfs://R2/projects/regds_antifraud/hdfs/prod/anti_crawler/user_id_redundancy_event_seq/'
  anticrawler_label_path: 'hdfs://D2/projects/antifraud_app_tss/hive/antifraud_app_tss/adm_anticrawler_model_sample_all_final_di/'
  wide_w_label_path: 'hdfs://R2/projects/regds_antifraud/hdfs/prod/anti_crawler/wide_w_label/'
  user_seq_wide_label_di_path: 'hdfs://R2/projects/regds_antifraud/hdfs/prod/anti_crawler/user_seq_wide_label_di'
  user_seq_wide_label_hi_path: 'hdfs://R2/projects/regds_antifraud/hdfs/prod/anti_crawler/user_seq_wide_label_hi'
  user_seq_wide_tt_label_di_path: 'hdfs://R2/projects/regds_antifraud/hdfs/prod/anti_crawler/user_seq_wide_tt_label_di'
  user_seq_wide_full_di_path: 'hdfs://R2/projects/regds_antifraud/hdfs/prod/anti_crawler/user_seq_wide_full_di'
  user_seq_wide_label_merge_tem_path: 'hdfs://R2/projects/regds_antifraud/hdfs/prod/anti_crawler/user_seq_wide_label_merge_tem'
  user_seq_wide_label_merge_tem_v2_path: 'hdfs://R2/projects/regds_antifraud/hdfs/prod/anti_crawler/user_seq_wide_label_merge_tem_v2'
  user_seq_wide_label_v2_path: 'hdfs://R2/projects/regds_antifraud/hdfs/prod/anti_crawler/user_seq_wide_label_v2'
  user_seq_wide_label_file_system_path: '/home/<USER>/ds-personal-sunfu-us/model_log/anti-crawler-user-seq-training/training_sample/'
  user_seq_wide_label_biglist_path: 'hdfs://R2/projects/regds_antifraud/hdfs/prod/anti_crawler/user_seq_wide_label_biglist'
  tss_eda_raw_path: 'hdfs://R2/projects/regds_antifraud/hdfs/prod/anti_crawler/tss_eda_raw'
  login_eda_raw_path: 'hdfs://R2/projects/regds_antifraud/hdfs/prod/anti_crawler/login_eda_raw'
  tss_eda_indexed_path: 'hdfs://R2/projects/regds_antifraud/hdfs/prod/anti_crawler/tss_eda_indexed'
  login_eda_indexed_path: 'hdfs://R2/projects/regds_antifraud/hdfs/prod/anti_crawler/login_eda_indexed'
  model_sample_all_final_di_v2_path: 'hdfs://D2/projects/antifraud_app_tss/hive/antifraud_app_tss/adm_anticrawler_model_sample_all_final_di_v2'
  training_sample_path: 'hdfs://R2/projects/regds_antifraud/hdfs/prod/anti_crawler/training_sample'
  validation_full_sample_path: 'hdfs://R2/projects/regds_antifraud/hdfs/prod/anti_crawler/validation_full_sample'
  validation_appeal_sample_path: 'hdfs://R2/projects/regds_antifraud/hdfs/prod/anti_crawler/validation_appeal_sample'
  standard_scaler_path: 'hdfs://R2/projects/regds_antifraud/hdfs/prod/generic_user/cbk/standard_scaler'
  standard_path: './standard_params'
  predict_aip_test_path: 'hdfs://R2/projects/regds_antifraud/hdfs/prod/generic_user/cbk/predict_aip'
  predict_table: 'regds_antifraud.generic_seq_cbk_model_predict_score_di'
  predict_date_range_path: 'hdfs://R2/projects/regds_antifraud/hdfs/prod/generic_user/cbk/predict_date_range/'
  data_point_installed_app: '/projects/data_inhouse/shopee/data_point/data_type=installed_app/'
  monitor_cbk_rate_table: 'regds_antifraud.generic_seq_model_performance_monitor_cbk_rate_di'
  monitor_block_cbk_table: 'regds_antifraud.generic_seq_model_performance_monitor_block_cbk_di'
  monitor_price_range_block_cbk_table: 'regds_antifraud.generic_seq_model_performance_monitor_price_range_block_cbk_di'
  monitor_ops_price_range_block_cbk_table: 'regds_antifraud.generic_seq_model_performance_ops_price_range_block_cbk_di'
  monitor_approval_rate_table: 'regds_antifraud.generic_seq_model_performance_monitor_approval_rate_di'


tables:
  offline_predict_path_table: regds_antifraud.dwd_anti_crawler_seq_model_offline_predict_di
  seq_model_online_log: antifraud_dw.dwd_ac_user_behavior_sequence_inference_di
  data_point_installed_decode_app: antifraud_pfd.data_point_installed_app_di
  registration_attr_table: regds_antifraud.dwd_anti_crawler_bss_registration_attr_di
  login_attr_table: regds_antifraud.dwd_anti_crawler_bss_login_attr_di
  tss_attr_table: regds_antifraud.dwd_anti_crawler_tss_action_attr_di
  tss_attr_raw_table: regds_antifraud.dwd_anti_crawler_tss_action_attr_w_raw_refer_di
  xgb_feature_table: security_data.adm_anticrawler_model_sample_fea_union_di
  xgb_online_feature_table: security_data.adm_anticrawler_model_fea_online_di_v2
  traning_label_table: antifraud_app_tss.adm_anticrawler_model_sample_all_final_f_di_v2
  traning_black_tt_table: antifraud_app_tss.adm_anticrawler_model_sample_black_tt_di
  tss_re_request_id_table: regds_antifraud.dwd_anti_crawler_tss_re_request_id_di
  raw_tss_backfill_table: regds_antifraud.dwd_anti_crawler_tss_action_attr_w_raw_refer_backfill_di
  user_seq_wide_label_table: regds_antifraud.dwd_anti_crawler_user_seq_wide_label_2_di
  user_seq_wide_label_table_path: hdfs://R2/projects/regds_antifraud/hive/regds_antifraud/dwd_anti_crawler_user_seq_wide_label_2_di
  user_seq_wide_label_us_table_path: hdfs://D2/projects/regds_antifraud/hdfs/ingestion/hi/prod/regds_antifraud.dwd_anti_crawler_user_seq_wide_label_2_di
  user_seq_wide_label_us_table_path_test: hdfs://D2/projects/regds_antifraud/hive/regds_antifraud/dwd_anti_crawler_user_seq_wide_label_2_di__test_1
  reduce_data_path: hdfs://R2/projects/regds_antifraud/hive/regds_antifraud/dwd_anti_crawler_user_seq_wide_label_2_di



#  labeled related action set for positive or negative(remain current payment, but need to remove the label related columns )
labeld_action_feats_dict:
  action_type: action_type__-99901

feature_attr:
  target_inference_start_hour: 0
  target_inference_end_hour: 24
  is_training: 0 # pull feature for training or validation; training pull less white
  event_seq_days_lookback: 1
  event_seq_millseconds_lookback: 6*60*60*1000
  user_entity_event_cnt: 1000
  other_entities_event_cnt: 20
  all_entities: [ user_id ]
  seq_diff_attr: [ diff_ctime,client_ip,dfpinfosz__securitydeviceid,shopeedf,webrtc_ip,new_session_id,shop_id,item_id,query_limit,query_offset,newest,matchid,categoryid ]
  seq_diff_attr_type: [ numeric_log, category, category, category, category, category, category, category,numeric_log,numeric_log,numeric_log,category,category ] # diff_ctime:numeric to log normal
  seq_diff_raw_attr: [ client_ip,dfpinfosz__securitydeviceid,shopeedf,webrtc_ip,new_session_id,shop_id,item_id,query_limit,query_offset,newest,matchid,categoryid ]
  all_shared_attr: [ event_id, ip_country,if_dfpinfosz__tags ]
  all_shared_attr_type: [ category, category, category ]
  tss_bert_attr: [ bert_referer_host_path,bert_keyword_norm_api_path ]
  tss_bert_attr_type: [ bert_embed,bert_embed ]
  login_attr: [
      platform_id
    , login_channel
    , LoginTag_New_User_IP_Clustering_1d_v1
    , is_new_ip_city
    , is_new_user
    , is_new_szdid
    , if_minority_email ]
  login_attr_type: [
      category
    , category
    , category
    , category
    , category
    , category
    , category ]
  tss_attr: [
      bins_query_limit
    , bins_offset
    , bins_newest
    , bins_cookie_length
    , platform
    , attributes__enhance_sap__sdk_version
    , attributes__cookie_info__language
    , is_hook
    , is_sapid_inconsistency
    , ntp
    , risk_tag_sap_corr
    , cate_decode_referer_host
    , cate_keyword_norm_referer_path
    , cate_keyword_norm_api_path
  ]
  tss_attr_type: [
      category
    , category
    , category
    , category
    , category
    , category
    , category
    , category
    , category
    , category
    , category
    , category
    , category
    , category
  ]
  xgb_seq_to_index: [
      fraud_tag_alltagcnts
    , fraud_tag_regtagcnts
    , fraud_tag_logintagcnts
    , fraud_tag_highrisk
  ]
  xgb_seq_attr: [
      is_sessionidsap_null
    , is_deviceid_null
    , is_webrtcip_null
    , is_cross_country_ip_domain
    , is_cross_country_ip_webrtcip
    , is_cross_country_domain_webrtcip
    , is_ip_webrtcip_diff
    , is_ipc_webrtcipc_diff
    , is_ipcountry_market
    , is_webrtcipcountry_market
    , is_night
    , is_bind_phone
    , is_bind_email
    , is_phone_verified
    , is_email_verified
    , fraud_tag_alltagcnts
    , fraud_tag_regtagcnts
    , fraud_tag_logintagcnts
    , fraud_tag_highrisk
    , is_userid_virtualphonenumber
    , is_cn_proxy_ip
  ]
  xgb_seq_attr_type: [
      category
    , category
    , category
    , category
    , category
    , category
    , category
    , category
    , category
    , category
    , category
    , category
    , category
    , category
    , category
    , category
    , category
    , category
    , category
    , category
    , category
  ]
  registration_wide_feature: [
      RegistrationTag_Web_Device_User_Clustering_1d_v1
    , RegistrationTag_Web_Device_User_Clustering_1d_v2
    , RegistrationTag_Web_Device_IP_Clustering_7d_v1
    , RegistrationTag_Web_Device_IP_Clustering_1d_v1
    , RegistrationTag_Web_Incognito_Mode
    , RegistrationTag_SZDID_Linkage_7d_v1
  ]
  exclude_seq_attr: [
      platform
    , attributes__enhance_sap__sdk_version
    , attributes__cookie_info__language
    , is_sessionidsap_null
    , is_deviceid_null
    , is_webrtcip_null
  ]

TIME_DIFFERENCE: {
    'SG': 0
  , 'MY': 0
  , 'TH': 1
  , 'TW': 0
  , 'ID': 1
  , 'VN': 1
  , 'PH': 0
  , 'BR': 11
  , 'PL': 6
}