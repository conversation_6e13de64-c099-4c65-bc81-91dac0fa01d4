username: fu.sunfu

network:
  n_label: 2
  wide_field_dims: [ 0 ]
  wide_fm_output_units: 512
  mlp_dims: [ 512, 512, 256 ]
  mlp_dropout: [ 0.30, 0.30, 0.30 ]
  wide_w_seq_output_units: 64
  user_entity_bert_event_attr_len: 0
  user_entity_event_attr_len: 0
  other_entity_event_attr_len: 0
  user_entity_event_cnt: 0
  other_entities_event_cnt: 0
  transformer_embedding_size: 384 # input feature embedding dimension & refer api bert emebed dimension
  nhid: 512 # the number of the feedforw network model in nn.TransformerEncoder
  nlayers: 4 # the number of nn.TransformerEncoderLayer in TransformerEncoder
  nhead: 2 # the number of heads in the multiheadattention models
  dropout: 0.1 # transformer dropout
  use_gpu: 0
  use_fm: 1
  network_form: 'transformer_wide'

training:
  debug: 0 # training data from csv
  base_epoch: 3 # model load last time saved weight,and continue training
  save_best_epoch: 1
  gpu_parallel: 1
  device_id: 0
  epoch: 6
  validation_epoch: 1
  batch_size: 8
  val_batch_size: 32
  pred_batch_size: 1024
  learning_rate: 0.0000001
  save_freq: 100
  print_freq: 100
  tb_folder: '/home/<USER>/ds-personal-sunfu-us/model_log/anti-crawler-user-seq-training/log'
  save_folder: '/home/<USER>/ds-personal-sunfu-us/model_log/anti-crawler-user-seq-training/model_weight/'
  fraud_ratio: 10
  training_start_date: '2024-05-20'
  training_end_date: '2024-05-27'
  validate_start_date: '2024-05-20'
  validate_end_date: '2024-05-27'
  test_start_date: '2024-05-29'
  test_end_date: '2024-05-29'

predict:
  model_file_path: 'hdfs://R2/projects/regds_antifraud/hdfs/prod/generic_user/cbk/model_weights_v1_0/transformer_wide_best_epoch_25_best_acc_0.9593_2022_12_28_1621.pth'

crawler_standard_params:
  feats_ids_dict_file: './crawler_standard_params/prod_feats_ids_dict.json'
  lib_refer_path_keywords: './crawler_standard_params/lib_refer_path_keywords.pkl'
  lib_api_path_keywords: './crawler_standard_params/lib_api_path_keywords.pkl'
  wide_stddev: './crawler_standard_params/wide_stddev.json'
  wide_mean: './crawler_standard_params/wide_mean.json'

path:
  predict_path: 'hdfs://R2/projects/regds_antifraud/hive/regds_antifraud/dwd_anti_crawler_seq_model_predict_di'
  predict_file_system_path: '/home/<USER>/ds-personal-sunfu-us/model_log/anti-crawler-user-seq-training/predict'
  predict_us_path: 'hdfs://D2/projects/regds_antifraud/hive/regds_antifraud/dwd_anti_crawler_seq_model_predict_di'
  dwd_anti_crawler_tss_refer_api_df_path: 'hdfs://R2/projects/regds_antifraud/hive/regds_antifraud/dwd_anti_crawler_tss_refer_api_df'
  refer_api_bert_embed_path: 'hdfs://R2/projects/regds_antifraud/hdfs/prod/anti_crawler/refer_api_bert_embed_df/'
  user_id_ids_val_seq_path: 'hdfs://R2/projects/regds_antifraud/hdfs/prod/anti_crawler/user_ids_val_seq'
  user_id_redundancy_event_seq_path: 'hdfs://R2/projects/regds_antifraud/hdfs/prod/anti_crawler/user_id_redundancy_event_seq/'
  anticrawler_label_path: 'hdfs://D2/projects/antifraud_app_tss/hive/antifraud_app_tss/adm_anticrawler_model_sample_all_final_di/'
  wide_w_label_path: 'hdfs://R2/projects/regds_antifraud/hdfs/prod/anti_crawler/wide_w_label/'
  user_seq_wide_label_path: 'hdfs://R2/projects/regds_antifraud/hdfs/prod/anti_crawler/user_seq_wide_label'
  user_seq_wide_label_file_system_path: '/home/<USER>/ds-personal-sunfu-us/model_log/anti-crawler-user-seq-training/training_sample/'
  user_seq_wide_label_biglist_path: 'hdfs://R2/projects/regds_antifraud/hdfs/prod/anti_crawler/user_seq_wide_label_biglist'
  tss_eda_raw_path: 'hdfs://R2/projects/regds_antifraud/hdfs/prod/anti_crawler/tss_eda_raw'
  login_eda_raw_path: 'hdfs://R2/projects/regds_antifraud/hdfs/prod/anti_crawler/login_eda_raw'
  tss_eda_indexed_path: 'hdfs://R2/projects/regds_antifraud/hdfs/prod/anti_crawler/tss_eda_indexed'
  login_eda_indexed_path: 'hdfs://R2/projects/regds_antifraud/hdfs/prod/anti_crawler/login_eda_indexed'
  model_sample_all_final_di_v2_path: 'hdfs://D2/projects/antifraud_app_tss/hive/antifraud_app_tss/adm_anticrawler_model_sample_all_final_di_v2'
  training_data_path: 'hdfs://R2/projects/regds_antifraud/hdfs/prod/generic_user/cbk/training_data'
  training_daily_data_path: 'hdfs://R2/projects/regds_antifraud/hdfs/prod/generic_user/cbk/daily_training_data'
  standard_scaler_path: 'hdfs://R2/projects/regds_antifraud/hdfs/prod/generic_user/cbk/standard_scaler'
  standard_path: './standard_params'
  predict_aip_test_path: 'hdfs://R2/projects/regds_antifraud/hdfs/prod/generic_user/cbk/predict_aip'
  predict_table: 'regds_antifraud.generic_seq_cbk_model_predict_score_di'
  predict_date_range_path: 'hdfs://R2/projects/regds_antifraud/hdfs/prod/generic_user/cbk/predict_date_range/'
  data_point_installed_app: '/projects/data_inhouse/shopee/data_point/data_type=installed_app/'
  monitor_cbk_rate_table: 'regds_antifraud.generic_seq_model_performance_monitor_cbk_rate_di'
  monitor_block_cbk_table: 'regds_antifraud.generic_seq_model_performance_monitor_block_cbk_di'
  monitor_price_range_block_cbk_table: 'regds_antifraud.generic_seq_model_performance_monitor_price_range_block_cbk_di'
  monitor_ops_price_range_block_cbk_table: 'regds_antifraud.generic_seq_model_performance_ops_price_range_block_cbk_di'
  monitor_approval_rate_table: 'regds_antifraud.generic_seq_model_performance_monitor_approval_rate_di'


tables:
  data_point_installed_decode_app: antifraud_pfd.data_point_installed_app_di
  registration_attr_table: regds_antifraud.dwd_anti_crawler_bss_registration_attr_di
  login_attr_table: regds_antifraud.dwd_anti_crawler_bss_login_attr_di
  tss_attr_table: regds_antifraud.dwd_anti_crawler_tss_action_attr_di
  tss_attr_raw_table: regds_antifraud.dwd_anti_crawler_tss_action_attr_w_raw_refer_di
  xgb_feature_table: security_data.adm_anticrawler_model_sample_fea_union_di
  traning_label_table: antifraud_app_tss.adm_anticrawler_model_sample_all_final_f_di
  tss_re_request_id_table: regds_antifraud.dwd_anti_crawler_tss_re_request_id_di
  raw_tss_backfill_table: regds_antifraud.dwd_anti_crawler_tss_action_attr_w_raw_refer_backfill_di
  user_seq_wide_label_table: regds_antifraud.dwd_anti_crawler_user_seq_wide_label_2_di
  user_seq_wide_label_table_path: hdfs://R2/projects/regds_antifraud/hive/regds_antifraud/dwd_anti_crawler_user_seq_wide_label_2_di
  user_seq_wide_label_us_table_path: hdfs://D2/projects/regds_antifraud/hdfs/ingestion/hi/prod/regds_antifraud.dwd_anti_crawler_user_seq_wide_label_2_di
  user_seq_wide_label_us_table_path_test: hdfs://D2/projects/regds_antifraud/hive/regds_antifraud/dwd_anti_crawler_user_seq_wide_label_2_di__test_1
  reduce_data_path: hdfs://R2/projects/regds_antifraud/hive/regds_antifraud/dwd_anti_crawler_user_seq_wide_label_2_di
  user_seq_wide_label_dataset_table_path:

#  labeled related action set for positive or negative(remain current payment, but need to remove the label related columns )
labeld_action_feats_dict:
  action_type: action_type__-99901

feature_attr:
  is_training: 0
  event_seq_days_lookback: 1
  event_seq_millseconds_lookback: 6*60*60*1000
  user_entity_event_cnt: 1000
  other_entities_event_cnt: 20
  all_entities: [ user_id ]
  seq_diff_attr: [ diff_ctime,client_ip,dfpinfosz__securitydeviceid,shopeedf,webrtc_ip,new_session_id,shop_id,item_id,query_limit,query_offset,newest,matchid,categoryid ]
  seq_diff_attr_type: [ numeric_log, category, category, category, category, category, category, category,numeric_log,numeric_log,numeric_log,category,category ] # diff_ctime:numeric to log normal
  seq_diff_raw_attr: [ client_ip,dfpinfosz__securitydeviceid,shopeedf,webrtc_ip,new_session_id,shop_id,item_id,query_limit,query_offset,newest,matchid,categoryid ]
  all_shared_attr: [ event_id, ip_country,if_dfpinfosz__tags ]
  all_shared_attr_type: [ category, category, category ]
  tss_bert_attr: [ bert_referer_host_path,bert_keyword_norm_api_path ]
  tss_bert_attr_type: [ bert_embed,bert_embed ]
  login_attr: [
      platform_id
    , login_channel
    , LoginTag_New_User_IP_Clustering_1d_v1
    , is_new_ip_city
    , is_new_user
    , is_new_szdid
    , if_minority_email ]
  login_attr_type: [
      category
    , category
    , category
    , category
    , category
    , category
    , category ]
  tss_attr: [
      bins_query_limit
    , bins_offset
    , bins_newest
    , bins_cookie_length
    , platform
    , attributes__enhance_sap__sdk_version
    , attributes__cookie_info__language
    , is_hook
    , is_sapid_inconsistency
    , ntp
    , risk_tag_sap_corr
    , cate_decode_referer_host
    , cate_keyword_norm_referer_path
    , cate_keyword_norm_api_path
  ]
  tss_attr_type: [
      category
    , category
    , category
    , category
    , category
    , category
    , category
    , category
    , category
    , category
    , category
    , category
    , category
    , category
  ]
  xgb_seq_to_index: [
      fraud_tag_alltagcnts
    , fraud_tag_regtagcnts
    , fraud_tag_logintagcnts
    , fraud_tag_highrisk
  ]
  xgb_seq_attr: [
      is_sessionidsap_null
    , is_deviceid_null
    , is_webrtcip_null
    , is_cross_country_ip_domain
    , is_cross_country_ip_webrtcip
    , is_cross_country_domain_webrtcip
    , is_ip_webrtcip_diff
    , is_ipc_webrtcipc_diff
    , is_ipcountry_market
    , is_webrtcipcountry_market
    , is_night
    , is_bind_phone
    , is_bind_email
    , is_phone_verified
    , is_email_verified
    , fraud_tag_alltagcnts
    , fraud_tag_regtagcnts
    , fraud_tag_logintagcnts
    , fraud_tag_highrisk
    , is_userid_virtualphonenumber
    , is_cn_proxy_ip
  ]
  xgb_seq_attr_type: [
      category
    , category
    , category
    , category
    , category
    , category
    , category
    , category
    , category
    , category
    , category
    , category
    , category
    , category
    , category
    , category
    , category
    , category
    , category
    , category
    , category
  ]
  registration_wide_feature: [
      RegistrationTag_Web_Device_User_Clustering_1d_v1
    , RegistrationTag_Web_Device_User_Clustering_1d_v2
    , RegistrationTag_Web_Device_IP_Clustering_7d_v1
    , RegistrationTag_Web_Device_IP_Clustering_1d_v1
    , RegistrationTag_Web_Incognito_Mode
    , RegistrationTag_SZDID_Linkage_7d_v1
  ]
  xgb_wide_feature: [
      i_countDistinct_api_per_user_1h
    , i_count_request_sensitive_per_user_1h
    , i_count_request_shopeedf_null_per_userid_1h
    , i_count_request_deviceid_null_per_userid_1h
    , i_count_request_sessionidsap_null_per_userid_1h
    , i_count_request_sessionidold_null_per_userid_1h
    , i_count_request_sessionidnew_null_per_userid_1h
    , i_count_request_webrtcip_null_per_userid_1h
    , i_count_request_crosscountry_ip_domain_per_userid_1h
    , i_count_request_crosscountry_ip_webrtcip_per_userid_1h
    , i_count_request_crosscountry_domain_webrtcip_per_userid_1h
    , i_count_request_ip_webrtcip_diff_per_userid_1h
    , i_count_request_ipc_webrtcipc_diff_per_userid_1h
    , i_count_request_ipcountry_market_per_userid_1h
    , i_count_request_webrtcipcountry_market_per_userid_1h
    , i_max_newest_per_userid_1h
    , i_sum_limit_per_userid_1h
    , i_max_offset_per_userid_1h
    , i_count_request_cn_proxy_ip_per_userid_1h
    , i_countDistinct_item_per_userid_1h
    , i_countDistinct_shop_per_userid_1h
    , i_countDistinct_ip_per_userid_1h
    , i_countDistinct_ipcountry_per_userid_1h
    , i_countDistinct_device_per_userid_1h
    , i_countDistinct_shopeedf_per_userid_1h
    , i_countDistinct_sessionsap_per_userid_1h
    , i_countDistinct_sessionnew_per_userid_1h
    , i_countDistinct_sessionold_per_userid_1h
    , i_countDistinct_webrtcip_per_userid_1h
    , i_countDistinct_webrtcipcountry_per_userid_1h
    , i_count_request_per_ip_1h
    , i_countDistinct_userid_per_ip_1h
    , i_countDistinct_userid_shopeedf_notnull_per_ip_1h
    , i_countDistinct_userid_deviceid_notnull_per_ip_1h
    , i_countDistinct_userid_sessionidnew_notnull_per_ip_1h
    , i_countDistinct_userid_sessionidold_notnull_per_ip_1h
    , i_countDistinct_userid_sessionidsap_notnull_per_ip_1h
    , i_countDistinct_userid_webrtcip_notnull_per_ip_1h
    , i_countDistinct_userid_pcweb_per_ip_1h
    , i_countDistinct_userid_newdevice_per_ip_1h
    , i_countDistinct_userid_reqcnts_gt50_per_ip_1h
    , i_count_request_per_webrtcip_1h
    , i_countDistinct_userid_per_webrtcip_1h
    , i_countDistinct_userid_shopeedf_notnull_per_webrtcip_1h
    , i_countDistinct_userid_deviceid_notnull_per_webrtcip_1h
    , i_countDistinct_userid_sessionidnew_notnull_per_webrtcip_1h
    , i_countDistinct_userid_sessionidold_notnull_per_webrtcip_1h
    , i_countDistinct_userid_sessionidsap_notnull_per_webrtcip_1h
    , i_countDistinct_userid_webrtcip_notnull_per_webrtcip_1h
    , i_countDistinct_userid_pcweb_per_webrtcip_1h
    , i_countDistinct_userid_newdevice_per_webrtcip_1h
    , i_countDistinct_userid_reqcnts_gt50_per_webrtcip_1h
    , i_countDistinct_hour_per_userid_6h
    , i_countDistinct_min_per_userid_6h
  ]

TIME_DIFFERENCE: {
    'SG': 0
  , 'MY': 0
  , 'TH': 1
  , 'TW': 0
  , 'ID': 1
  , 'VN': 1
  , 'PH': 0
  , 'BR': 11
  , 'PL': 6
}