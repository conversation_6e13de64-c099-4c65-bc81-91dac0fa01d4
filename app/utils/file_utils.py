"""
Utility functions for file handling.
"""
import os
import uuid
import mimetypes
from pathlib import Path
from typing import BinaryIO, Optional, Set, <PERSON><PERSON>

import httpx
from fastapi import HTT<PERSON><PERSON>x<PERSON>, UploadFile

from app.core.config import settings


# Set of allowed mime types
ALLOWED_MIME_TYPES: Set[str] = {
    "application/pdf",
    "application/vnd.openxmlformats-officedocument.wordprocessingml.document",  # docx
    "application/vnd.openxmlformats-officedocument.presentationml.presentation",  # pptx
    "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",  # xlsx
    "text/plain",
    "text/markdown",
    "text/html",
    "application/msword",  # doc
    "application/vnd.ms-excel",  # xls
    "application/vnd.ms-powerpoint",  # ppt
}


def validate_file_content(file: UploadFile) -> Tuple[bool, Optional[str]]:
    """Validate file content type.
    
    Args:
        file: The uploaded file to validate.
        
    Returns:
        A tuple of (is_valid, error_message).
    """
    # Check file size
    file.file.seek(0, os.SEEK_END)
    file_size = file.file.tell()
    file.file.seek(0)  # Reset file pointer
    
    if file_size > settings.MAX_UPLOAD_SIZE:
        return False, f"File too large. Maximum size is {settings.MAX_UPLOAD_SIZE // 1024 // 1024}MB"
    
    # Check mime type
    content_type = file.content_type
    if content_type not in ALLOWED_MIME_TYPES:
        return False, f"File type '{content_type}' not allowed"
    
    # Additional security check - verify extension matches content type
    extension = os.path.splitext(file.filename)[1].lower() if file.filename else ""
    expected_types = {mtype for mtype, ext in mimetypes.types_map.items() if ext.lower() == extension}
    
    if extension and expected_types and content_type not in expected_types:
        return False, "File extension does not match content type"
    
    return True, None


def ensure_upload_dir() -> None:
    """Ensure the upload directory exists."""
    os.makedirs(settings.UPLOAD_DIR, exist_ok=True)


def generate_temp_file_path(filename: str) -> str:
    """Generate a temporary file path for an uploaded file.
    
    Args:
        filename: The original filename.
        
    Returns:
        A temporary file path.
    """
    # Extract file extension
    _, ext = os.path.splitext(filename)
    
    # Generate a unique filename
    unique_filename = f"{uuid.uuid4()}{ext}"
    
    # Return the full path
    return os.path.join(settings.UPLOAD_DIR, unique_filename)


async def save_upload_file(upload_file: UploadFile) -> str:
    """Save an uploaded file to disk.
    
    Args:
        upload_file: The uploaded file.
        
    Returns:
        The path to the saved file.
    """
    # Validate file
    is_valid, error = validate_file_content(upload_file)
    if not is_valid:
        raise HTTPException(status_code=400, detail=error)
    
    # Ensure the upload directory exists
    ensure_upload_dir()
    
    # Generate a temporary file path
    temp_file_path = generate_temp_file_path(upload_file.filename)
    
    # Save the file
    with open(temp_file_path, "wb") as f:
        content = await upload_file.read()
        f.write(content)
    
    return temp_file_path


async def download_file(url: str) -> Optional[str]:
    """Download a file from a URL.
    
    Args:
        url: The URL to download from.
        
    Returns:
        The path to the downloaded file, or None if the download failed.
    """
    # Ensure the upload directory exists
    ensure_upload_dir()
    
    try:
        # Generate a temporary file path
        temp_file_path = os.path.join(settings.UPLOAD_DIR, f"{uuid.uuid4()}")
        
        # Download the file
        async with httpx.AsyncClient(timeout=30.0, follow_redirects=True) as client:
            response = await client.get(url)
            response.raise_for_status()
            
            # Check content type
            content_type = response.headers.get("Content-Type", "").split(";")[0]
            if content_type not in ALLOWED_MIME_TYPES:
                return None
            
            # Check file size
            if len(response.content) > settings.MAX_UPLOAD_SIZE:
                return None
            
            # Try to get the filename from the Content-Disposition header
            content_disposition = response.headers.get("Content-Disposition", "")
            if "filename=" in content_disposition:
                filename = content_disposition.split("filename=")[1].strip('"')
                # Update the temp file path with the extension
                _, ext = os.path.splitext(filename)
                if ext:
                    temp_file_path = f"{temp_file_path}{ext}"
            
            # Save the file
            with open(temp_file_path, "wb") as f:
                f.write(response.content)
        
        return temp_file_path
    except Exception:
        return None


def cleanup_file(file_path: str) -> None:
    """Clean up a temporary file.
    
    Args:
        file_path: The path to the file to clean up.
    """
    try:
        if os.path.exists(file_path):
            os.remove(file_path)
    except Exception:
        pass  # Ignore errors during cleanup
