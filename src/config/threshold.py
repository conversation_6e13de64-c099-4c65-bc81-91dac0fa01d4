from dataclasses import dataclass
from typing import Dict, List


@dataclass
class ThresholdConfig:
    threshold_dict: Dict[str, float]
    list: str


def parse_thresholds(inputs: dict) -> Dict[str, List[ThresholdConfig]]:
    ret = {}
    for k, v in inputs.items():
        tcs = []
        for record in v:
            lst = record["list"]
            thresholds = record["thresholds"]
            t = ThresholdConfig(thresholds, lst)
            tcs.append(t)

        ret[k] = tcs

    return ret
