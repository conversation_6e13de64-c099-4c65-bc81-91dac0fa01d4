import os

import torch

from src.utils.config import parse_config

LOG_PATH = os.getenv('LOG_PATH') or "./log"
ROOT_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Consumer main data source
REQUEST_KAFKA_USERNAME = os.getenv('REQUEST_KAFKA_USERNAME')
REQUEST_KAFKA_PASSWORD = os.getenv('REQUEST_KAFKA_PASSWORD')
REQUEST_KAFKA_TOPIC = os.getenv('REQUEST_KAFKA_TOPIC')
REQUEST_KAFKA_BOOTSTRAP_SERVERS = os.getenv('REQUEST_KAFKA_BOOTSTRAP_SERVERS')
REQUEST_KAFKA_CONSUMER_GROUP = os.getenv('REQUEST_KAFKA_CONSUMER_GROUP')

# Retry kafka for error cases
ERROR_LOGGER_KAFKA_BOOTSTRAP_SERVERS = os.getenv("ERROR_LOGGER_KAFKA_BOOTSTRAP_SERVERS")
ERROR_LOGGER_KAFKA_USERNAME = os.getenv("ERROR_LOGGER_KAFKA_USERNAME")
ERROR_LOGGER_KAFKA_PASSWORD = os.getenv("ERROR_LOGGER_KAFKA_PASSWORD")
ERROR_LOGGER_KAFKA_TOPIC = os.getenv("ERROR_LOGGER_KAFKA_TOPIC")

# Model related configurations.
MODEL_VERSION = int(os.getenv("MODEL_VERSION") or 1)
MODEL_STATE_PATH = os.getenv("MODEL_STATE_PATH")
IS_USE_GPU = torch.cuda.is_available()
DEVICE = torch.device(f"cuda:{torch.cuda.current_device()}") if IS_USE_GPU else torch.device("cpu")
config = parse_config(ROOT_DIR, MODEL_VERSION, IS_USE_GPU)

# Exporter related configurations.
TC_HTTP_API_ENDPOINT = os.getenv("TC_HTTP_API_ENDPOINT")
TC_CALLER_SDU = os.getenv("TC_CALLER_SDU")
TC_CALLER_SERVICE_KEY = os.getenv("TC_CALLER_SERVICE_KEY")
TC_CALLER_TIMEOUT_MS = os.getenv("TC_CALLER_TIMEOUT_MS")
TC_CALLER_USER_COUNT_LIMIT = int(os.getenv("TC_CALLER_USER_COUNT_LIMIT") or 1000)

# Model Logger
LOGGER_KAFKA_BOOTSTRAP_SERVERS = os.getenv("LOGGER_KAFKA_BOOTSTRAP_SERVERS")
LOGGER_KAFKA_USERNAME = os.getenv("LOGGER_KAFKA_USERNAME")
LOGGER_KAFKA_PASSWORD = os.getenv("LOGGER_KAFKA_PASSWORD")
LOGGER_KAFKA_TOPIC = os.getenv("LOGGER_KAFKA_TOPIC")

INSTANCE_ID = f"{os.getenv('podName')}-{os.getenv('serviceVersion')}"
ENV = os.getenv('AIP_DEVELOPMENT_ENV')
ENV_PROD = "prod"

SPEX_CONFIG_ITEM_KEY = os.getenv('SPEX_CONFIG_ITEM_KEY')

print(f"Env: {ENV}, Instance ID: {INSTANCE_ID}, is using GPU: {IS_USE_GPU}")
