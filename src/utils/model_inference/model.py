import os
import logging
import json
import torch.nn as nn
import torch
import numpy as np
import sys
from torch.nn.parallel import DistributedDataParallel as DDP

from src.constant.constant import ROOT_DIR
from src.utils.model_inference.transformer_model import TransformerModel

this_dir = os.path.dirname(os.path.abspath(__file__))
root_dir = ROOT_DIR
sys.path.insert(0, root_dir)


class FactorizationMachine(torch.nn.Module):

    def __init__(self, reduce_sum=True):
        super().__init__()
        self.reduce_sum = reduce_sum

    def forward(self, x):
        """
        :param x: Float tensor of size ``(batch_size, num_fields, embed_dim)``
        :return size ``(batch_size, embed_dim)``
        """
        square_of_sum = torch.sum(x, dim=1) ** 2
        sum_of_square = torch.sum(x ** 2, dim=1)
        ix = square_of_sum - sum_of_square
        if self.reduce_sum:
            ix = torch.sum(ix, dim=1, keepdim=True)
        return 0.5 * ix


class FeaturesLinear(torch.nn.Module):

    def __init__(self, field_dims, output_dim=1):
        """
        output_dim:embedding size(output units)
        """
        super().__init__()
        self.fc = torch.nn.Embedding(sum(field_dims), output_dim)
        self.bias = torch.nn.Parameter(torch.zeros((output_dim,)))
        self.offsets = np.array((0, *np.cumsum(field_dims)[:-1]), dtype=np.longlong)
        self.dropout = nn.Dropout(0.3)

    def forward(self, vals, ids):
        """
        :param x: Long tensor of size ``(batch_size, num_fields)``
        """
        vals = vals + vals.new_tensor(self.offsets).unsqueeze(0)
        _x = torch.mul(self.fc(ids), vals.reshape(-1, vals.shape[1], 1))  # batch*num_fields*embedding_size
        x = torch.sum(_x, dim=1) + self.bias  # batch*embedding_size
        x = self.dropout(x)
        return x


class FeaturesEmbedding(torch.nn.Module):

    def __init__(self, field_dims, embed_dim):
        super().__init__()
        self.embedding = torch.nn.Embedding(sum(field_dims), embed_dim)
        self.offsets = np.array((0, *np.cumsum(field_dims)[:-1]), dtype=np.longlong)
        torch.nn.init.xavier_uniform_(self.embedding.weight.data)

    def forward(self, x):
        """
        :param x: Long tensor of size ``(batch_size, num_fields)``
        """
        x = x + x.new_tensor(self.offsets).unsqueeze(0)
        return self.embedding(x)


class FactorizationMachineModel(torch.nn.Module):
    """
    A pytorch implementation of Factorization Machine.
    Reference:
        S Rendle, Factorization Machines, 2010.
    :return size ``(batch_size, embed_dim)``
    """

    def __init__(self, field_dims, embed_dim=256, use_gpu=0):
        super().__init__()
        self.use_gpu = use_gpu
        self.field_dims = field_dims
        self.embedding = FeaturesEmbedding(field_dims, embed_dim)
        self.linear = FeaturesLinear(field_dims, embed_dim)
        self.fm = FactorizationMachine(reduce_sum=False)
        self.dropout = nn.Dropout(0.3)

    def forward(self, x):
        """
        :param x: Long tensor of size ``(batch_size, num_fields)``
        """
        vals = x
        wide_1d_ids = np.arange(sum(self.field_dims))
        wide_2d_ids = np.tile(wide_1d_ids, (vals.shape[0], 1))
        ids = torch.from_numpy(wide_2d_ids).long()
        if self.use_gpu:
            ids = ids.cuda()
        linear_output = self.linear(vals, ids)
        fm_input = torch.mul(self.embedding(ids), vals.reshape(-1, vals.shape[1], 1))
        fm_output = self.fm(fm_input)
        # x = linear_output + fm_output
        x = torch.cat((linear_output, fm_output), dim=1)
        x = self.dropout(x)
        return x


class LineClassifier(nn.Module):
    def __init__(self, config):
        super(LineClassifier, self).__init__()
        self.all_entities_len = len(config['all_entities'])
        self.network_form = config['network']['network_form']
        self.mlp = nn.Sequential()
        wide_mlp_dropout = config['network']['mlp_dropout']
        input_dim = config['network']['wide_field_dims'][0]
        for i, dim in enumerate(config['network']['mlp_dims']):
            self.mlp.add_module("layer_%d" % i, nn.Linear(input_dim, dim))
            self.mlp.add_module("bn_%d" % i, nn.BatchNorm1d(dim))
            self.mlp.add_module("rule_%d" % i, nn.ReLU())
            self.mlp.add_module("dropout_%d" % i, nn.Dropout(wide_mlp_dropout[i]))
            input_dim = dim

        if self.network_form == 'wide_only':
            self.layer1 = nn.Linear(
                config['network']['mlp_dims'][-1],
                config['network']['wide_w_seq_output_units'])
        elif self.network_form == 'transformer_only':
            self.layer1 = nn.Linear(
                self.all_entities_len * config['network']['transformer_embedding_size'],
                config['network']['wide_w_seq_output_units'])
        else:
            self.layer1 = nn.Linear(
                config['network']['mlp_dims'][-1] + self.all_entities_len * config['network'][
                    'transformer_embedding_size'],
                config['network']['wide_w_seq_output_units'])
        self.bn1 = nn.BatchNorm1d(config['network']['wide_w_seq_output_units'])
        self.rule1 = nn.ReLU()
        self.dropout_1 = nn.Dropout(0.3)

        self.classifier = nn.Linear(config['network']['wide_w_seq_output_units'], config['network']['n_label'])
        self.init_weights()

    def init_weights(self):
        for m in self.modules():
            initrange = 0.1
            if isinstance(m, nn.Linear):
                # nn.init.xavier_normal_(m.weight)
                m.weight.data.uniform_(-initrange, initrange)
                nn.init.constant_(m.bias, 0)

    def forward(self, seq_x, wide_x):
        mlp_output = self.mlp(wide_x)
        if self.network_form == 'wide_only':
            cat_feat = mlp_output
        elif self.network_form == 'transformer_only':
            cat_feat = seq_x
        else:
            cat_feat = torch.cat((seq_x, mlp_output), dim=1)
        cat_out_1 = self.layer1(cat_feat)
        cat_out_2 = self.bn1(cat_out_1)
        cat_out_3 = self.rule1(cat_out_2)
        cat_out_4 = self.dropout_1(cat_out_3)
        return self.classifier(cat_out_4)


class LineClassifier2(nn.Module):
    def __init__(self, config, wide_fm_model):
        super(LineClassifier2, self).__init__()
        self.gpu_parallel = config['training']['gpu_parallel']
        self.network_form = config['network']['network_form']
        wide_mlp_dropout = config['network']['mlp_dropout']
        self.wide_fm = wide_fm_model
        self.mlp = nn.Sequential()
        if self.network_form == 'wide_only':
            input_dim = 2 * config['network']['wide_fm_output_units']  # linear size + fm size
        elif self.network_form == 'transformer_only':
            input_dim = self.all_entities_len * config['network']['transformer_embedding_size']
        else:
            input_dim = 2 * config['network']['wide_fm_output_units'] + config['network']['transformer_embedding_size']

        for i, dim in enumerate(config['network']['mlp_dims']):
            self.mlp.add_module("layer_%d" % i, nn.Linear(input_dim, dim))
            # self.mlp.add_module("bn_%d" % i, nn.BatchNorm1d(dim)) #TODO
            self.mlp.add_module("layernorm_%d" % i, nn.LayerNorm(dim))
            self.mlp.add_module("rule_%d" % i, nn.ReLU())
            self.mlp.add_module("dropout_%d" % i, nn.Dropout(wide_mlp_dropout[i]))
            input_dim = dim

        layer1 = nn.Linear(config['network']['mlp_dims'][-1],
                           config['network']['wide_w_seq_output_units'])
        layernorm1 = nn.LayerNorm(config['network']['wide_w_seq_output_units'])
        rule1 = nn.ReLU()
        dropout_1 = nn.Dropout(0.3)
        self.concat_layer = nn.Sequential(layer1, rule1, layernorm1, dropout_1)

        self.classifier = nn.Linear(config['network']['wide_w_seq_output_units'], config['network']['n_label'])
        self.init_weights()

    def init_weights(self):
        for m in self.modules():
            if isinstance(m, nn.Linear):
                nn.init.xavier_normal_(m.weight)
                nn.init.constant_(m.bias, 0)

    def forward(self, seq_x, wide_x):
        wide_fm_output = self.wide_fm(wide_x)
        if self.network_form == 'wide_only':
            cat_feat = wide_fm_output
        elif self.network_form == 'transformer_only':
            cat_feat = seq_x
        else:
            cat_feat = torch.cat((seq_x, wide_fm_output), dim=1)
        cat_mlp_output = self.mlp(cat_feat)
        cat_out = self.concat_layer(cat_mlp_output)
        return self.classifier(cat_out)


def set_model(config):
    feats_ids_dict_file = os.path.join(root_dir, config['crawler_standard_params']['feats_ids_dict_file'])
    if os.path.exists(feats_ids_dict_file):
        with open(feats_ids_dict_file, 'r') as load_f:
            voc_dict = json.load(load_f)
            ntokens = len(voc_dict)  # the size of vocabulary
            logging.info("字典数:" + str(ntokens))
    else:
        raise ValueError('训练字典json文件不存在:{}'.format(feats_ids_dict_file))
    emsize = config['network']['transformer_embedding_size']  # embedding dimension
    nhid = config['network']['nhid']  # the number of the feedforw network model in nn.TransformerEncoder
    nlayers = config['network']['nlayers']  # the number of nn.TransformerEncoderLayer in TransformerEncoder
    nhead = config['network']['nhead']  # the number of heads in the multiheadattention models
    dropout = config['network']['dropout']
    use_fm = config['network']['use_fm']
    user_entity_event_attr_len = config['network']['user_entity_event_attr_len']
    bert_event_attr_len = config['network']['user_entity_bert_event_attr_len']
    user_entity_event_cnt = config['network']['user_entity_event_cnt']

    model = TransformerModel(ntokens, emsize, nhead, nhid, nlayers, event_attr_len=user_entity_event_attr_len,
                             event_cnt=user_entity_event_cnt, use_fm=use_fm, bert_event_attr_len=bert_event_attr_len,
                             dropout=dropout)

    wide_fm_model = FactorizationMachineModel(config['network']['wide_field_dims'],
                                              config['network']['wide_fm_output_units'],
                                              config['network']['use_gpu'])
    classifier = LineClassifier2(config, wide_fm_model)

    # classifier = LineClassifier(config)
    # Duke: Model inference doesn't need loss calculation.
    # criterion = nn.CrossEntropyLoss()
    if config['network']['use_gpu']:
        model = model.cuda()
        classifier = classifier.cuda()
        # criterion = criterion.cuda()
        # Duke: No DistributedDataParallel needed since it's not training but inference.
        # if config['training']['gpu_parallel']:
        #     model = torch.nn.parallel.DistributedDataParallel(model, find_unused_parameters=True)
        #     classifier = torch.nn.parallel.DistributedDataParallel(classifier, find_unused_parameters=True)
    # return model, classifier, criterion
    return model, classifier


def set_only_wide_model(config):
    wide_fm_model = FactorizationMachineModel(config['network']['wide_field_dims'],
                                              config['network']['wide_fm_output_units'],
                                              config['network']['use_gpu'])
    # classifier = LineClassifier(config)
    classifier = LineClassifier2(config, wide_fm_model)
    criterion = nn.CrossEntropyLoss()
    if config['network']['use_gpu']:
        classifier = classifier.cuda()
        criterion = criterion.cuda()
    return classifier, criterion
