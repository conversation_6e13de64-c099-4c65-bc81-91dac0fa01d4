r"""Treament Center data model."""


class ActionBodyInfo:
    def __init__(self, reason, dependencies=None):
        if dependencies is None:
            dependencies = []
        self.id = 0
        self.reason = reason
        self.dependencies = dependencies

    def to_dict(self):
        return {
            'id': self.id,
            'reason': self.reason,
            'dependencies': self.dependencies
        }


class AddDataToListServiceActionBody:
    def __init__(self, data, list_name):
        self.data = data
        self.list_name = list_name

    def to_dict(self):
        return {
            'data': self.data,
            'list_name': self.list_name
        }


class ActionBodyList:
    def __init__(self, action_body_info, add_data_to_list_service_action_body):
        self.action_body_info = action_body_info
        self.add_data_to_list_service_action_body = add_data_to_list_service_action_body

    def to_dict(self):
        return {
            'action_body_info': self.action_body_info.to_dict(),
            'add_data_to_list_service_action_body':
                self.add_data_to_list_service_action_body.to_dict()
        }


class ActionBodyListWrapper:
    def __init__(self, action_body_list):
        self.action_body_list = action_body_list

    def to_dict(self):
        return {
            'action_body_list': [item.to_dict()
                                 for item in self.action_body_list]
        }


class ActionResultInfo:
    def __init__(self, id, result_status, result_message):
        self.id = id
        self.result_status = result_status
        self.result_message = result_message

    def to_dict(self):
        return {
            'id': self.id,
            'result_status': self.result_status,
            'result_message': self.result_message,
        }


class ChangeUserAccountStatusResult:
    def __init__(self, is_all_success):
        self.is_all_success = is_all_success

    def to_dict(self):
        return {'is_all_success': self.is_all_success}


class ActionResult:
    def __init__(self, action_result_info, change_user_account_status_result):
        self.action_result_info = action_result_info
        self.change_user_account_status_result = change_user_account_status_result

    def to_dict(self):
        return {
            'action_result_info': self.action_result_info.to_dict(),
            'change_user_account_status_result':
                self.change_user_account_status_result.to_dict()
        }
