import torch

from src.utils.model_inference.transformer_model import TransformerModel


# Replace with your model’s class definition
class YourModelClass(torch.nn.Module):
    def __init__(self):
        super(YourModelClass, self).__init__()
        # Define your model architecture, e.g.:
        self.layer = torch.nn.Linear(input_size, output_size)  # Adjust as needed

    def forward(self, x):
        # Define the forward pass
        return self.layer(x)  # Adjust as needed


# Load the checkpoint
model = TransformerModel(1,1,1,1,1,1,1, 1,1)
checkpoint_path = "/Users/<USER>/Working/Shopee/user-behavior-sequence/model_weights/transformer_wide_best_epoch_3_best_acc_0.986_2024_11_02_2158.pth"
checkpoint = torch.load(checkpoint_path, map_location=torch.device('cpu'))

# Load weights
if isinstance(checkpoint, dict):  # Likely a state_dict
    model.load_state_dict(checkpoint)
else:  # Entire model
    model = checkpoint
model.eval()

# Convert to TorchScript
sample_input = torch.randn(1, 60)  # Adjust input_size to match your model
traced_model = torch.jit.trace(model, sample_input)
# Save TorchScript model
torch.jit.save(traced_model, "model_repository/ubs_pytorch/1/model.pt")