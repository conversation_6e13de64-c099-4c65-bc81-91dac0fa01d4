# Copyright (c) 2020, NVIDIA CORPORATION. All rights reserved.
#
# Redistribution and use in source and binary forms, with or without
# modification, are permitted provided that the following conditions
# are met:
#  * Redistributions of source code must retain the above copyright
#    notice, this list of conditions and the following disclaimer.
#  * Redistributions in binary form must reproduce the above copyright
#    notice, this list of conditions and the following disclaimer in the
#    documentation and/or other materials provided with the distribution.
#  * Neither the name of NVIDIA CORPORATION nor the names of its
#    contributors may be used to endorse or promote products derived
#    from this software without specific prior written permission.
#
# THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS ``AS IS'' AND ANY
# EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
# IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR
# PURPOSE ARE DISCLAIMED.  IN NO EVENT SHALL THE COPYRIGHT OWNER OR
# CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, <PERSON>ECIAL,
# EXEMPLARY, OR <PERSON><PERSON><PERSON>QUENTIAL DAMAGES (INCLUDING, BUT NOT <PERSON>IMITED TO,
# PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR
# PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY
# OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
# (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
# OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.

name: "simple_repeat"
max_batch_size: 0
platform: "ensemble"
ensemble_scheduling {
  step [
    {
      model_name: "repeat_int32"
      model_version: -1
      input_map {
        key: "IN"
        value: "IN"
      }
      input_map {
        key: "DELAY"
        value: "DELAY"
      }
      input_map {
        key: "WAIT"
        value: "WAIT"
      }
      output_map {
        key: "OUT"
        value: "OUT"
      }
      output_map {
        key: "IDX"
        value: "IDX"
      }
    }
  ]
}
input [
  {
    name: "IN"
    data_type: TYPE_INT32
    dims: [ -1 ]
  },
  {
    name: "DELAY"
    data_type: TYPE_UINT32
    dims: [ -1 ]
  },
  {
    name: "WAIT"
    data_type: TYPE_UINT32
    dims: [ 1 ]
  }
]
output [
  {
    name: "OUT"
    data_type: TYPE_INT32
    dims: [ 1 ]
  },
  {
    name: "IDX"
    data_type: TYPE_UINT32
    dims: [ 1 ]
  }
]
