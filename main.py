"""
Main FastAPI application for the MarkItDown API.
"""
import os
import time
from contextlib import asynccontextmanager
from fastapi import FastAP<PERSON>, HTTPException, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from typing import Dict, List, Tuple

from app.api.routes import router as api_router
from app.core.config import settings
from app.utils.file_utils import ensure_upload_dir


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Define startup and shutdown logic."""
    # Ensure the upload directory exists during startup
    ensure_upload_dir()
    yield
    # Cleanup operations can go here if needed


# Create the FastAPI application
app = FastAPI(
    title=settings.PROJECT_NAME,
    openapi_url=f"{settings.API_V1_STR}/openapi.json",
    lifespan=lifespan,
)

# Rate limiting state
# Store IP: (list of request timestamps, last cleanup time)
rate_limit_store: Dict[str, Tuple[List[float], float]] = {}


# Add Rate limiting middleware
@app.middleware("http")
async def rate_limit_middleware(request: Request, call_next):
    """Rate limiting middleware."""
    client_ip = request.client.host if request.client else "unknown"

    # Initialize or get the request timestamps list for this IP
    if client_ip not in rate_limit_store:
        rate_limit_store[client_ip] = ([], time.time())

    timestamps, last_cleanup = rate_limit_store[client_ip]

    # Clean up old request timestamps (more than RATE_LIMIT_DURATION old)
    current_time = time.time()
    if current_time - last_cleanup > settings.RATE_LIMIT_DURATION:
        cutoff_time = current_time - settings.RATE_LIMIT_DURATION
        timestamps = [ts for ts in timestamps if ts > cutoff_time]
        rate_limit_store[client_ip] = (timestamps, current_time)

    # Check if rate limit is exceeded
    if len(timestamps) >= settings.MAX_REQUESTS_PER_MINUTE:
        return JSONResponse(
            status_code=429,
            content={"success": False, "error": "Rate limit exceeded. Please try again later."}
        )

    # Add the current request timestamp
    timestamps.append(current_time)
    rate_limit_store[client_ip] = (timestamps, last_cleanup)

    # Process the request
    return await call_next(request)


# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.BACKEND_CORS_ORIGINS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


# Add exception handlers
@app.exception_handler(HTTPException)
async def http_exception_handler(request, exc):
    """Handle HTTP exceptions."""
    return JSONResponse(
        status_code=exc.status_code,
        content={"success": False, "error": exc.detail},
    )


@app.exception_handler(Exception)
async def general_exception_handler(request, exc):
    """Handle general exceptions."""
    return JSONResponse(
        status_code=500,
        content={"success": False, "error": str(exc)},
    )


# Include API routes
app.include_router(api_router, prefix=settings.API_V1_STR)


# Root endpoint
@app.get("/")
async def root():
    """Root endpoint."""
    return {"message": "Welcome to the MarkItDown API. See /docs for API documentation."}


if __name__ == "__main__":
    import uvicorn

    # Get the port from the environment or use 8000 as default
    port = int(os.getenv("PORT", "8000"))

    # Run the application
    uvicorn.run("main:app", host="0.0.0.0", port=port, reload=True)
